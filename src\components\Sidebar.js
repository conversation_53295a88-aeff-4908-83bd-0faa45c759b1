
import { NavLink, useLocation } from 'react-router-dom';
import {
  FiHome,
  FiUpload,
  FiSearch,
  FiActivity,
  FiUser,
  FiHelpCircle,
  FiLogOut,
  FiMenu,
  FiX
} from 'react-icons/fi';
import { useTheme } from '../contexts/ThemeContext';
import './Sidebar.css';

const Sidebar = ({ collapsed, onToggle }) => {
  const location = useLocation();
  const { isDarkMode } = useTheme();

  const menuItems = [
    { path: '/', icon: FiHome, label: 'Dashboard', color: '#3b82f6' },
    { path: '/upload', icon: FiUpload, label: 'Upload Resume', color: '#10b981' },
    { path: '/search', icon: FiSearch, label: 'Search Resumes', color: '#f59e0b' },
    // DISABLED: All Resumes menu item - To re-enable, uncomment the line below
    // { path: '/resumes', icon: FiUsers, label: 'All Resumes', color: '#8b5cf6' },
    { path: '/activity', icon: FiActivity, label: 'Activity Log', color: '#ef4444' },
    { path: '/profile', icon: FiUser, label: 'Profile', color: '#6b7280' },
    { path: '/support', icon: FiHelpCircle, label: 'Support', color: '#06b6d4' }
  ];

  const handleLogout = () => {
    // Add logout functionality here
    console.log('Logout clicked');
  };

  return (
    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      {/* Header */}
      <div className="sidebar-header">
        <div className="logo-container">
          {!collapsed ? (
            <div className="logo-text">
              <img
                src={isDarkMode ? '/Images/AVLogo-white.png' : '/Images/AVLogo-black.png'}
                alt="AV Logo"
                className="logo-icon"
                onError={(e) => {
                  console.error('Logo failed to load:', e.target.src);
                  e.target.src = '/Images/AVLogo-black.png'; // Fallback
                }}
              />
              <span className="logo-title">Resume AI</span>
            </div>
          ) : (
            <div className="logo-collapsed" onClick={onToggle} title="Expand sidebar">
              <img
                src={isDarkMode ? '/Images/AVLogo-white.png' : '/Images/AVLogo-black.png'}
                alt="AV Logo"
                className="logo-icon clickable"
                onError={(e) => {
                  console.error('Logo failed to load:', e.target.src);
                  e.target.src = '/Images/AVLogo-black.png'; // Fallback
                }}
              />
            </div>
          )}
        </div>
        <button
          className="toggle-btn"
          onClick={onToggle}
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {collapsed ? <FiMenu size={20} /> : <FiX size={20} />}
        </button>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <ul className="nav-list">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <li key={item.path} className="nav-item">
                <NavLink
                  to={item.path}
                  className={`nav-link ${isActive ? 'active' : ''}`}
                  title={collapsed ? item.label : ''}
                >
                  <div className="nav-icon" style={{ color: isActive ? item.color : '#6b7280' }}>
                    <Icon size={20} />
                  </div>
                  {!collapsed && (
                    <span className="nav-label">{item.label}</span>
                  )}
                  {isActive && <div className="active-indicator" style={{ backgroundColor: item.color }} />}
                </NavLink>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="sidebar-footer">
        <button 
          className="logout-btn"
          onClick={handleLogout}
          title={collapsed ? 'Log Out' : ''}
        >
          <div className="nav-icon">
            <FiLogOut size={20} />
          </div>
          {!collapsed && <span className="nav-label">Log Out</span>}
        </button>
        
        {!collapsed && (
          <div className="sidebar-info">
            <div className="version-info">
              <span className="version-text">Version 1.0</span>
              <span className="powered-by">Powered by GPT-4.1</span>
              <span className="version-text">Developed By: AccuVelocity</span>
              
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
