#!/usr/bin/env python3
"""
Test script to verify the upload functionality works correctly
"""

import requests
import json

def test_upload():
    """Test the upload endpoint with a simple PDF"""
    
    # Create a simple test PDF content
    test_pdf_content = b'''%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test Resume Content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
293
%%EOF'''

    print("🧪 Testing upload endpoint...")
    
    try:
        # Test the upload endpoint
        files = {'files': ('test_new_upload.pdf', test_pdf_content, 'application/pdf')}
        response = requests.post('http://localhost:8002/api/upload', files=files, timeout=60)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Upload successful!")
            print(f"📁 Uploaded files: {data.get('uploaded_files', 0)}")
            print(f"🗄️ MongoDB stored files: {data.get('mongodb_stored_files', 0)}")
            print(f"⬇️ Files immediately downloadable: {data.get('files_immediately_downloadable', False)}")
            
            if data.get('mongodb_files'):
                print("📋 MongoDB file info:")
                for file_info in data['mongodb_files']:
                    print(f"   - {file_info['filename']}: {file_info['resume_id']} ({file_info['status']})")
                    
                    # Test download for the first file
                    resume_id = file_info['resume_id']
                    print(f"\n🔽 Testing download for resume ID: {resume_id}")
                    
                    download_response = requests.get(f'http://localhost:8002/api/resumes/{resume_id}/download', timeout=30)
                    print(f"📊 Download response status: {download_response.status_code}")
                    
                    if download_response.status_code == 200:
                        print("✅ Download successful!")
                        print(f"📄 Downloaded file size: {len(download_response.content)} bytes")
                        
                        # Verify content type
                        content_type = download_response.headers.get('Content-Type', '')
                        print(f"📋 Content type: {content_type}")
                        
                        # Check if it's the same content
                        if download_response.content == test_pdf_content:
                            print("✅ Downloaded content matches uploaded content!")
                        else:
                            print("⚠️ Downloaded content differs from uploaded content")
                            
                    else:
                        print(f"❌ Download failed: {download_response.text}")
            else:
                print("⚠️ No MongoDB files in response")
                
        else:
            print(f"❌ Upload failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_upload()
