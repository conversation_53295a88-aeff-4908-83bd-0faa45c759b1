JANE SMITH
Frontend Developer

Contact Information:
Email: <EMAIL>
Phone: +****************
Address: 456 Oak Street, New York, NY 10001

PROFESSIONAL SUMMARY
Creative frontend developer with 3+ years of experience in building responsive web applications.
Passionate about user experience and modern web technologies.
Strong expertise in React, TypeScript, and CSS frameworks.

WORK EXPERIENCE

Frontend Developer - WebTech Solutions (2021-2023)
• Developed responsive web applications using React and TypeScript
• Collaborated with UX/UI designers to implement pixel-perfect designs
• Optimized application performance resulting in 30% faster load times
• Mentored junior developers and conducted code reviews

Junior Frontend Developer - Digital Agency (2020-2021)
• Built interactive user interfaces using HTML, CSS, and JavaScript
• Worked with REST APIs to integrate frontend with backend services
• Participated in agile development processes and daily standups
• Contributed to component library development

EDUCATION

Bachelor of Science in Computer Science
New York University (2016-2020)
GPA: 3.7/4.0

Relevant Coursework:
• Web Development
• Human-Computer Interaction
• Software Engineering
• Database Systems

TECHNICAL SKILLS

Programming Languages:
• JavaScript (ES6+)
• TypeScript
• HTML5
• CSS3
• Python (Basic)

Frameworks & Libraries:
• React.js
• Next.js
• Vue.js
• Redux
• Material-UI
• Tailwind CSS
• Bootstrap

Tools & Technologies:
• Git & GitHub
• Webpack
• npm/yarn
• Figma
• Adobe XD
• Chrome DevTools
• VS Code

PROJECTS

E-commerce Platform Frontend (2023)
• Built a modern e-commerce frontend using React and TypeScript
• Implemented shopping cart, product catalog, and checkout flow
• Integrated with Stripe payment API
• Achieved 95+ Lighthouse performance score

Portfolio Website (2022)
• Designed and developed personal portfolio website
• Used Next.js for server-side rendering and SEO optimization
• Implemented dark/light theme toggle
• Deployed on Vercel with custom domain

Task Management App (2021)
• Created a collaborative task management application
• Built with React and Firebase for real-time updates
• Implemented drag-and-drop functionality
• Added user authentication and role-based permissions

CERTIFICATIONS

• React Developer Certification - Meta (2022)
• Frontend Web Development - freeCodeCamp (2021)
• JavaScript Algorithms and Data Structures - freeCodeCamp (2020)

ACHIEVEMENTS

• Won "Best UI/UX Design" at University Hackathon 2019
• Contributed to open-source React component library with 500+ stars
• Speaker at local JavaScript meetup on "Modern CSS Techniques"
• Maintained 4.9/5 rating on freelance projects

LANGUAGES

• English (Native)
• Spanish (Conversational)
• French (Basic)

INTERESTS

• Web accessibility and inclusive design
• Open source contributions
• Photography and digital art
• Hiking and outdoor activities
