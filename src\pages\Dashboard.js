import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  FiUsers,
  FiUpload,
  FiSearch,
  FiActivity,
  FiTrendingUp,
  FiClock,
  FiFileText,
  FiArrowRight,
  FiRefreshCw,
  FiDownload,
  FiTrash2,
  FiSettings,
  FiCopy
} from 'react-icons/fi';
import { toast } from 'react-toastify';
import './Dashboard.css';

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalResumes: 0,
    searchResults: 0,
    aiPowered: 'GPT-4.1',
    version: 'v1.0'
  });

  const [recentActivity, setRecentActivity] = useState([]);
  const [quickStats, setQuickStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Utility function to format timestamp to relative time
  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return 'Just now';

    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now - activityTime) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return `${Math.floor(diffInSeconds / 604800)} weeks ago`;
  };

  // Enhanced function to format activity details
  const formatActivityDetail = (activity) => {
    const { action, detail } = activity;

    // Extract meaningful information from the detail
    if (action.includes('Resume uploaded') && detail) {
      return detail; // Show person name
    }
    if (action.includes('Resume processed successfully') && detail) {
      return detail; // Show person name
    }
    if (action.includes('Duplicate resume detected') && detail) {
      return detail; // Show person name
    }
    if (action.includes('Resume processing failed') && detail) {
      return detail; // Show filename
    }
    if (action.includes('Search completed') && detail) {
      // Extract query and results count from detail like '"Python developers" (15 results)'
      const match = detail.match(/^"([^"]+)"\s*\((\d+)\s*results?\)$/);
      if (match) {
        return `${match[1]} (${match[2]} results)`;
      }
      return detail;
    }
    if (action.includes('Documents processed') && detail) {
      return detail; // Show processing details
    }
    if (action.includes('PDF downloaded') && detail) {
      // Extract filename from detail like 'Resume ID: 123, File: filename.pdf'
      const match = detail.match(/File:\s*(.+)$/);
      if (match) {
        return match[1];
      }
      return detail;
    }

    return detail || '';
  };

  // Function to handle activity click
  const handleActivityClick = async (activity) => {
    const { action, detail } = activity;

    // Handle search activity clicks
    if (action.includes('Search completed')) {
      // Extract search query from detail like '"Python developers" (15 results)'
      const match = detail.match(/^"([^"]+)"/);
      if (match) {
        const searchQuery = match[1];
        // Navigate to search page with the query and switch to history tab
        navigate('/search', {
          state: {
            searchQuery: searchQuery,
            openHistoryTab: true
          }
        });
        return;
      }
    }

    // Handle successful resume processing clicks
    if (action.includes('Resume processed successfully')) {
      const personName = detail;
      try {
        // Search for the resume by person name
        const response = await fetch(`http://localhost:8002/query/${encodeURIComponent(personName)}`);
        if (response.ok) {
          const searchData = await response.json();
          if (searchData.results && searchData.results.length > 0) {
            // Navigate to search page with the person's name and show results
            navigate('/search', {
              state: {
                searchQuery: personName,
                searchResults: searchData.results,
                showResults: true
              }
            });
            return;
          }
        }
      } catch (error) {
        console.error('Error searching for resume:', error);
      }

      // DISABLED: All Resumes page navigation - To re-enable, uncomment the line below
      // navigate('/resumes');

      // Fallback: navigate to search page instead
      navigate('/search');
    }
  };

  // Function to check if activity is clickable
  const isActivityClickable = (activity) => {
    const { action } = activity;
    return action.includes('Search completed') || action.includes('Resume processed successfully');
  };

  // Function to fetch real statistics from backend
  const fetchRealStats = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/stats');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching stats:', error);
      return null;
    }
  };

  // Function to fetch real activity log from backend
  const fetchRealActivity = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/activity');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.activities || [];
    } catch (error) {
      console.error('Error fetching activity:', error);
      return [];
    }
  };

  // Function to fetch total resumes from backend
  const fetchTotalResumes = async () => {
    try {
      const response = await fetch('http://localhost:8002/showall');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return Array.isArray(data) ? data.length : 0;
    } catch (error) {
      console.error('Error fetching total resumes:', error);
      return 0;
    }
  };



  // Function to calculate real quick stats from backend data
  const calculateRealQuickStats = (realStats, totalResumes, activityData) => {
    if (!realStats) {
      // Fallback to basic stats if API fails
      return [
        {
          label: 'Total Resumes',
          value: totalResumes.toString(),
          change: totalResumes > 0 ? `+${Math.min(totalResumes, 5)}` : '0',
          trend: totalResumes > 0 ? 'up' : 'neutral'
        },
        {
          label: 'This Week',
          value: '0',
          change: '0',
          trend: 'neutral'
        },
        {
          label: 'Total Searches',
          value: '0',
          change: '0',
          trend: 'neutral'
        },
        {
          label: 'Activities',
          value: activityData.length.toString(),
          change: activityData.length > 0 ? `+${Math.min(activityData.length, 10)}` : '0',
          trend: activityData.length > 0 ? 'up' : 'neutral'
        }
      ];
    }

    // Calculate success rate based on activity log
    const uploadActivities = activityData.filter(activity =>
      activity.action.includes('uploaded') || activity.action.includes('processed')
    );
    const errorActivities = activityData.filter(activity =>
      activity.action.includes('error') || activity.action.includes('failed')
    );

    const successRate = uploadActivities.length > 0
      ? ((uploadActivities.length / (uploadActivities.length + errorActivities.length)) * 100).toFixed(1)
      : '100.0';

    // Use real recent uploads data from backend stats (most accurate)
    let recentUploads = realStats?.recent_uploads || 0;

    console.log(`📊 Using real backend data - This week: ${recentUploads}, Total resumes: ${totalResumes}`);

    return [
      {
        label: 'This Week',
        value: recentUploads.toString(),
        change: recentUploads > 0 ? `+${recentUploads}` : '0',
        trend: recentUploads > 0 ? 'up' : 'neutral'
      },
      {
        label: 'Total Searches',
        value: realStats.total_searches?.toString() || '0',
        change: realStats.total_searches > 0 ? `+${Math.min(realStats.total_searches, 10)}` : '0',
        trend: realStats.total_searches > 0 ? 'up' : 'neutral'
      },
      {
        label: 'Success Rate',
        value: `${successRate}%`,
        change: parseFloat(successRate) > 95 ? '+0.5%' : '0%',
        trend: parseFloat(successRate) > 95 ? 'up' : 'neutral'
      },
      {
        label: 'Total Activities',
        value: realStats.total_activities?.toString() || activityData.length.toString(),
        change: realStats.total_activities > 0 ? `+${Math.min(realStats.total_activities, 20)}` : '0',
        trend: realStats.total_activities > 0 ? 'up' : 'neutral'
      }
    ];
  };

  // Function to load all dashboard data
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      console.log('🔄 Loading real dashboard data...');

      // Fetch all data in parallel
      const [totalResumes, realStats, realActivity] = await Promise.all([
        fetchTotalResumes(),
        fetchRealStats(),
        fetchRealActivity()
      ]);

      console.log('📊 Real stats from backend:', realStats);
      console.log('📋 Real activity from backend:', realActivity);

      // Update main stats
      setStats({
        totalResumes: totalResumes,
        searchResults: realStats?.total_searches || 0,
        aiPowered: 'GPT-4.1',
        version: 'v1.0'
      });

      // Filter out system activities and use real activity data (limit to 5 most recent)
      const filteredActivity = realActivity.filter(activity => {
        const action = activity.action || '';
        return !action.includes('Sample data loaded') &&
               !action.includes('System started') &&
               !action.includes('Advanced processing completed');
      });

      const sortedActivity = filteredActivity
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 5);

      setRecentActivity(sortedActivity);

      // Calculate real quick stats
      setQuickStats(calculateRealQuickStats(realStats, totalResumes, realActivity));
      setLastUpdated(new Date());

      console.log('✅ Dashboard data loaded successfully');

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');

      // Fallback to basic data
      const totalResumes = await fetchTotalResumes();
      setStats({
        totalResumes: totalResumes,
        searchResults: 0,
        aiPowered: 'GPT-4.1',
        version: 'v1.0'
      });
      setRecentActivity([]);
      setQuickStats(calculateRealQuickStats(null, totalResumes, []));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const quickActions = [
    {
      title: 'Upload Resume',
      description: 'Add new resumes to your collection',
      icon: FiUpload,
      color: '#10b981',
      link: '/upload'
    },
    {
      title: 'Search Resumes',
      description: 'Find candidates using natural language',
      icon: FiSearch,
      color: '#f59e0b',
      link: '/search'
    },
    // DISABLED: View All Resumes quick action - To re-enable, uncomment the block below
    /*
    {
      title: 'View All Resumes',
      description: 'Browse your complete resume database',
      icon: FiUsers,
      color: '#8b5cf6',
      link: '/resumes'
    },
    */
    {
      title: 'Activity Log',
      description: 'Track all system activities',
      icon: FiActivity,
      color: '#ef4444',
      link: '/activity'
    }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'upload':
        return <FiUpload className="activity-icon upload" />;
      case 'search':
        return <FiSearch className="activity-icon search" />;
      case 'process':
        return <FiFileText className="activity-icon process" />;
      case 'bulk':
        return <FiUsers className="activity-icon bulk" />;
      case 'duplicate':
        return <FiCopy className="activity-icon duplicate" />;
      case 'download':
        return <FiDownload className="activity-icon download" />;
      case 'delete':
        return <FiTrash2 className="activity-icon delete" />;
      case 'export':
        return <FiArrowRight className="activity-icon export" />;
      case 'system':
        return <FiSettings className="activity-icon system" />;
      case 'error':
        return <FiActivity className="activity-icon error" />;
      default:
        return <FiActivity className="activity-icon default" />;
    }
  };

  return (
    <div className="dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-content">
          <h1>Welcome back! 👋</h1>
          <p>Here's what's happening with your Resume AI Agent today.</p>
        </div>
        <div className="welcome-stats">
          <div className="stat-item">
            <FiClock className="stat-icon" />
            <span>
              Last activity: {recentActivity.length > 0 ? formatTimeAgo(recentActivity[0].timestamp) : 'No recent activity'}
            </span>
          </div>
          <button
            className="refresh-btn"
            onClick={loadDashboardData}
            disabled={loading}
            title="Refresh dashboard data"
          >
            <FiRefreshCw className={loading ? 'spinning' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Main Stats */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-header">
            <FiUsers className="stat-icon" />
            <span className="stat-label">Total Resumes</span>
          </div>
          <div className="stat-value">{loading ? '...' : stats.totalResumes}</div>
          <div className="stat-change positive">
            <FiTrendingUp size={14} />
            <span>
              {quickStats.length > 0 && quickStats[0]
                ? `${quickStats[0].change} this week`
                : 'Loading...'}
            </span>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-header">
            <span className="version-icon">🚀</span>
            <span className="stat-label">Version</span>
          </div>
          <div className="stat-value">{stats.version}</div>
          <div className="stat-change positive">
            <span>Up to date</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions-section">
        <h2>Quick Actions</h2>
        <div className="quick-actions-grid">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Link key={index} to={action.link} className="action-card">
                <div className="action-icon" style={{ backgroundColor: `${action.color}15`, color: action.color }}>
                  <Icon size={24} />
                </div>
                <div className="action-content">
                  <h3>{action.title}</h3>
                  <p>{action.description}</p>
                </div>
                <FiArrowRight className="action-arrow" />
              </Link>
            );
          })}
        </div>
      </div>

      {/* Bottom Grid */}
      <div className="bottom-grid">
        {/* Recent Activity */}
        <div className="activity-section">
          <div className="section-header">
            <h2>Recent Activity</h2>
            <Link to="/activity" className="view-all-link">View all</Link>
          </div>
          <div className="activity-list">
            {recentActivity.length === 0 ? (
              <div className="activity-empty">
                <FiActivity className="empty-icon" />
                <p>No recent activity</p>
              </div>
            ) : (
              recentActivity.map((activity) => (
                <div
                  key={activity.id}
                  className={`activity-item ${isActivityClickable(activity) ? 'clickable' : ''}`}
                  onClick={() => isActivityClickable(activity) && handleActivityClick(activity)}
                  style={{ cursor: isActivityClickable(activity) ? 'pointer' : 'default' }}
                >
                  <div className="activity-icon-wrapper">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="activity-content">
                    <div className="activity-main">
                      <span className="activity-action">{activity.action}</span>
                      {formatActivityDetail(activity) && (
                        <span className="activity-detail">: {formatActivityDetail(activity)}</span>
                      )}
                    </div>
                    <div className="activity-meta">
                      <span className="activity-user">by {activity.user || 'Admin'}</span>
                      <span className="activity-time">{formatTimeAgo(activity.timestamp)}</span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="quick-stats-section">
          <div className="section-header">
            <h2>Quick Stats</h2>
          </div>
          <div className="quick-stats-grid">
            {loading ? (
              // Loading state
              Array.from({ length: 4 }, (_, index) => (
                <div key={index} className="quick-stat-item loading">
                  <div className="quick-stat-label">Loading...</div>
                  <div className="quick-stat-value">...</div>
                  <div className="quick-stat-change neutral">
                    <span>...</span>
                  </div>
                </div>
              ))
            ) : (
              quickStats.map((stat, index) => (
                <div key={index} className="quick-stat-item">
                  <div className="quick-stat-label">{stat.label}</div>
                  <div className="quick-stat-value">{stat.value}</div>
                  <div className={`quick-stat-change ${stat.trend}`}>
                    {stat.trend === 'up' && <FiTrendingUp size={12} />}
                    <span>{stat.change}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
