/**
 * Formats phone numbers based on their length
 * @param {string} phoneNumber - The phone number to format
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return 'Not Mentioned';
  
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Handle different phone number lengths
  if (digits.length === 10) {
    // Format as XXXXX XXXXX
    return `${digits.slice(0, 4)} ${digits.slice(4)}`;
  } else if (digits.length === 12) {
    // Format as XX XXXXX XXXXX
    return `${digits.slice(0, 2)} ${digits.slice(2, 7)} ${digits.slice(7)}`;
  } else {
    // Return original if it doesn't match expected lengths
    return phoneNumber;
  }
};
