import os
import streamlit as st
import pandas as pd
from io import StringIO
import requests
import json
import time
from pathlib import Path
from DisplayJson import CDisplayResume
from datetime import datetime
from extractDataParallel import CProcessDocument
from dotenv import load_dotenv
from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib

# Set page configuration at the very top
st.set_page_config(
    page_title="Resume AI Agent",
    layout="wide",
    page_icon="🤖",
    initial_sidebar_state="collapsed"
)

load_dotenv()

# Enhanced custom CSS for modern, attractive design
st.markdown(
    """
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Inter', sans-serif;
    }

    .block-container {
        padding-top: 0.5rem;
        padding-bottom: 1rem;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        margin: 0.5rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        max-height: 100vh;
        overflow-y: auto;
    }

    /* Header Styles */
    header {visibility: hidden;}

    /* Title Styling */
    .main-title {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 3rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }

    .subtitle {
        text-align: center;
        color: #6c757d;
        font-size: 1.2rem;
        margin-bottom: 2rem;
        font-weight: 400;
    }

    /* Card Styles */
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    /* Button Styles */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* Form Styles */
    .stTextInput > div > div > input {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .stTextInput > div > div > input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* File Uploader Styles */
    .stFileUploader > div {
        border: 2px dashed #667eea;
        border-radius: 12px;
        padding: 1rem;
        background: rgba(102, 126, 234, 0.05);
        transition: all 0.3s ease;
        min-height: 80px;
    }

    .stFileUploader > div:hover {
        border-color: #764ba2;
        background: rgba(118, 75, 162, 0.05);
    }

    /* Compact Section */
    .compact-section {
        margin: 0.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    /* Reduce spacing between elements */
    .stTextInput > div {
        margin-bottom: 0.5rem;
    }

    .stButton > button {
        margin-top: 0.5rem;
    }

    /* Make form elements more compact */
    .stForm {
        border: none;
        padding: 0;
    }

    .stForm > div {
        gap: 0.5rem;
    }

    /* Compact Ready Section */
    .compact-ready-section {
        background: rgba(102, 126, 234, 0.05);
        border: 1px solid rgba(102, 126, 234, 0.1);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
        text-align: center;
    }

    /* Result Container */
    .result-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        margin: 1rem 0;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    /* Resume Card Styles */
    .resume-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .resume-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    /* Status and Toast Styles */
    .stAlert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Progress Bar */
    .stProgress > div > div {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
    }

    /* Sidebar Styles */
    .css-1d391kg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Compact Hero */
    .compact-hero {
        text-align: center;
        margin-bottom: 1rem;
    }

    .compact-title {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 2.2rem;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
    }

    .compact-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin: 0.3rem 0 1rem 0;
        font-weight: 400;
    }

    /* Compact Metrics */
    .compact-metric {
        background: white;
        padding: 0.8rem;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin: 0.3rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .compact-metric:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .metric-icon {
        font-size: 1.2rem;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        color: white;
        flex-shrink: 0;
    }

    .metric-content {
        flex: 1;
        text-align: left;
    }

    .metric-label {
        font-size: 0.75rem;
        color: #6c757d;
        font-weight: 500;
        margin: 0;
        line-height: 1;
    }

    .metric-value {
        font-size: 1rem;
        color: #212529;
        font-weight: 600;
        margin: 0;
        line-height: 1.2;
    }

    /* Search Header */
    .search-header {
        margin-bottom: 0.8rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    }

    /* Animation Classes */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .main-title {
            font-size: 2rem;
        }
        .block-container {
            padding: 1rem;
            margin: 0.5rem;
        }
    }
    </style>
    """,
    unsafe_allow_html=True
)

# strApiUrl = "http://127.0.0.1:8503/query/"
STR_API_URL = os.getenv('STR_API_URL')
# STR_API_URL_FETCH_ALL = os.getenv('STR_API_URL_FETCH_ALL')
STR_API_URL_FETCH_ALL = "http://localhost:8002/showall"

# Use session state to store uploaded files
if "uploaded_files" not in st.session_state:
    st.session_state["uploaded_files"] = None

# uploader_key = "pdf_uploader"

def process_uploaded_pdfs():
    """Simulates PDF processing with notifications"""
    with st.status("📄 Processing uploaded PDFs...", expanded=True) as status:
        objCProcessDocument = CProcessDocument(strVendorName="Resume_Schema")
        objCProcessDocument.MprocessAllDocuments(iFilesToProcess=1)

        # MakeReadableResponseData.MReadableResponseData()

        status.update(label="✅ Processing completed!", state="complete", expanded=False)
        st.toast("🎯 All PDFs processed successfully!", icon="✅")
    # Reset the UI after processing
    time.sleep(3)  # Wait for a moment before clearing
    st.session_state["uploaded_files"] = None
    # Remove the file uploader key and force a rerun to clear it
    if "pdf_uploader" in st.session_state:
        del st.session_state["pdf_uploader"]
    st.rerun()  # Refresh the Streamlit app to reset the uploader


# Helper function to extract 10th marks from a resume
def extract_10th_marks(resume):
    try:
        for education in resume["Resume"]["Education"]:
            degree = education["Degree"].lower()
            if "high school" in degree or "10th" in degree:
                marks = education["GPA/Marks/%"]
                try:
                    marks_float = float(marks)
                    if marks_float > 0:  # Only consider positive values as valid marks
                        return marks_float
                except ValueError:
                    pass  # Skip if marks cannot be converted to float
    except (KeyError, TypeError):
        pass  # Return None if resume structure is invalid
    return None


def calculate_checksum(file_data):
    """Calculate SHA-256 checksum from bytes."""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(file_data)  # Directly update hash with bytes
    return sha256_hash.hexdigest()


def main():
    # Initialize session state variables if they don't exist
    if "query_result" not in st.session_state:
        st.session_state["query_result"] = ""
    if "resumes" not in st.session_state:
        st.session_state["resumes"] = []  # List to store JSON resumes

    # Compact Hero Section
    st.markdown(
        """
        <div class="compact-hero fade-in-up">
            <h1 class="compact-title">🤖 Resume AI Agent</h1>
            <p class="compact-subtitle">Intelligent Resume Search & Management System</p>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Compact metrics in a single row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(
            """
            <div class="compact-metric">
                <div class="metric-icon">📊</div>
                <div class="metric-content">
                    <div class="metric-label">Total</div>
                    <div class="metric-value">""" + str(len(st.session_state.get("all_resumes", []))) + """</div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col2:
        st.markdown(
            """
            <div class="compact-metric">
                <div class="metric-icon">🔍</div>
                <div class="metric-content">
                    <div class="metric-label">Results</div>
                    <div class="metric-value">""" + str(len(st.session_state["resumes"])) + """</div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col3:
        st.markdown(
            """
            <div class="compact-metric">
                <div class="metric-icon">⚡</div>
                <div class="metric-content">
                    <div class="metric-label">AI</div>
                    <div class="metric-value">GPT-4</div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col4:
        st.markdown(
            """
            <div class="compact-metric">
                <div class="metric-icon">🚀</div>
                <div class="metric-content">
                    <div class="metric-label">Version</div>
                    <div class="metric-value">v2.0</div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

    # Compact Search Section
    with st.form(key='query_form'):
        st.markdown(
            """
            <div class="search-header">
                <h3 style="color: #667eea; margin: 0; font-size: 1.2rem;">🔍 Natural Language Search</h3>
            </div>
            """,
            unsafe_allow_html=True
        )

        col1, col2, col3 = st.columns([5, 1, 1])

        with col1:
            user_query = st.text_input(
                "",
                placeholder="e.g., 'Find software engineers with Python experience' or 'Show teachers with 5+ years experience'",
                key="user_query",
                label_visibility="collapsed"
            )

        with col2:
            submit_button = st.form_submit_button("🔍 Search", use_container_width=True)

        with col3:
            show_all = st.form_submit_button("📋 Show All", use_container_width=True)

    # Results placeholder
    output_placeholder = st.empty()

    if submit_button:
        user_query = st.session_state.user_query  # Get the current input value
        if not user_query:
            st.warning("Please enter a query before submitting.")
        else:
            try:
                response = requests.get(STR_API_URL, params={"naturalQuery": user_query})
                try:
                    data = response.json()  # Convert API response to JSON
                    if isinstance(data, list):  # If API returns a direct list
                        st.session_state["resumes"] = data
                        st.session_state["query_result"] = "Data received successfully."
                    elif isinstance(data, dict):  # If API returns a dictionary
                        st.session_state["resumes"] = data.get("listOfDict", [])
                        st.session_state["query_result"] = data.get("result", "Data received successfully.")
                    else:
                        st.session_state["query_result"] = "Unexpected API response format."
                except json.decoder.JSONDecodeError:
                    st.session_state["query_result"] = "API response is not valid JSON."

                # Developer mode output (optional)
                st.session_state["mongoQuery"] = data.get("query", [])
                st.write("Developer mode : API Response =", response, " | ", json.dumps(st.session_state["mongoQuery"]))
            except requests.exceptions.RequestException as e:
                st.error(f"An error occurred while connecting to the API: {e}")
                st.session_state["query_result"] = "Error connecting to API. Displaying sample data."

            # Update the output placeholder with enhanced result display
            with output_placeholder.container():
                if st.session_state["query_result"]:
                    st.markdown(
                        f"""
                        <div class="result-container fade-in-up">
                            <h3 style="margin: 0 0 1rem 0;">🎯 AI Search Results</h3>
                            <p style="margin: 0; font-size: 1.1rem; line-height: 1.6;">
                                {st.session_state['query_result']}
                            </p>
                            <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.3);">
                                <strong>📊 Found {len(st.session_state['resumes'])} matching resume(s)</strong>
                            </div>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
            # Note: Do NOT reset st.session_state.user_query here because it is managed by the widget.

    # Compact Upload Section
    st.markdown(
        """
        <div class="compact-section">
            <h3 style="color: #667eea; margin: 1rem 0 0.5rem 0; font-size: 1.2rem;">📁 Upload & Process Resumes</h3>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Define the folder where PDFs will be saved
    SAVE_PATH = r".\Data\inputData\Resume_Schema"  # Change this to your desired path
    os.makedirs(SAVE_PATH, exist_ok=True)
    MongoClient = MongoDBClient()

    # Create two columns for the upload and search sections
    col1, col2 = st.columns([1, 1])

    # Column 1: File Upload Section
    with col1:
        uploaded_files = st.file_uploader(
            "📤 Upload PDF Resumes",
            type=["pdf"],
            accept_multiple_files=True,
            key="pdf_uploader",
            help="Select one or more PDF resume files to upload and process",
            label_visibility="collapsed"
        )

        if uploaded_files:
            st.session_state["uploaded_files"] = [file.name for file in uploaded_files]  # Store files in session state
            if st.button("Save & Process PDFs", use_container_width=True):
                saved_files = []
                duplicate_files = []

                # File saving loop
                for uploaded_file in uploaded_files:

                    try:
                        file_data = bytes(uploaded_file.getbuffer())  # Convert memoryview to bytes
                        checksum = calculate_checksum(file_data)  # Compute checksum before saving

                        # Check if the checksum already exists in the database
                        if MongoClient.is_duplicate(checksum):
                            duplicate_files.append(uploaded_file.name)
                            continue  # Skip saving this file

                        file_path = Path(SAVE_PATH) / uploaded_file.name
                        saved_files.append(file_path)

                        with open(file_path, "wb") as f:
                            f.write(file_data)

                    except Exception as e:
                        st.error(f"❌ Error saving {uploaded_file.name}: {e}")

                if duplicate_files:
                    st.toast(f"⚠️ {len(duplicate_files)} Duplicate PDFs skipped")

                # ✅ Show a single toast after all files are saved
                if saved_files:
                    st.toast(f"✅ {len(saved_files)} PDF(s) saved successfully!", icon="📂")
                    process_uploaded_pdfs()  # Process after saving
    # Column 2: Universal Search Section
    sorted_resumes = []
    with col2:
        # Universal Search: Search within all resumes
        if st.session_state["resumes"]:
            # Universal search input that filters the resumes already in session state
            search_term = st.text_input(
                "🔍 Filter Results",
                key="resume_search",
                placeholder="Enter keywords to filter results...",
                help="Search within the current search results"
            )

            # Filter resumes that contain the search term (case-insensitive)
            if search_term:
                filtered_resumes = [
                    resume for resume in st.session_state["resumes"]
                    if search_term.lower() in json.dumps(resume).lower()
                ]
                st.success(f"🔍 Filtered to {len(filtered_resumes)} resume(s)")
            else:
                filtered_resumes = st.session_state["resumes"]

            # Sort the filtered resumes by 10th marks in descending order
            sorted_resumes = sorted(
                filtered_resumes,
                key=lambda x: extract_10th_marks(x) or 0,
                reverse=True
            )
        else:
            st.text_input(
                "🔍 Filter Results",
                key="resume_search_disabled",
                placeholder="Search for resumes first to enable filtering",
                disabled=True
            )

    # Enhanced "Show All" Section

    # Ensure session state key exists before accessing it
    if "all_resumes" not in st.session_state:
        st.session_state["all_resumes"] = []

    # Fetch all resumes from database
    try:
        response = requests.get(STR_API_URL_FETCH_ALL)
        try:
            data = response.json()
            st.session_state["all_resumes"] = data
        except json.decoder.JSONDecodeError:
            st.session_state["query_result"] = "API response is not valid JSON."
    except Exception as e:
        st.error(f"Error fetching resumes: {e}")

    all_resumes = [res for res in st.session_state["all_resumes"]]

    if show_all:
        st.markdown(
            """
            <div class="compact-section">
                <h3 style="color: #667eea; margin: 0.5rem 0; font-size: 1.2rem;">📋 All Resumes in Database</h3>
            </div>
            """,
            unsafe_allow_html=True
        )

        if all_resumes:
            st.success(f"📊 Displaying all {len(all_resumes)} resumes from the database")

            for i in range(0, len(all_resumes), 2):
                cols = st.columns(2)

                # Left resume
                with cols[0]:
                    CDisplayResume.display_resume_with_delete(all_resumes[i])

                # Right resume, if available
                if i + 1 < len(all_resumes):
                    with cols[1]:
                        CDisplayResume.display_resume_with_delete(all_resumes[i+1])
                else:
                    cols[1].empty()
        else:
            st.warning("📭 No resumes found in the database. Upload some resumes to get started!")

    # Enhanced Search Results Display
    if not show_all and st.session_state["resumes"]:
        st.markdown(
            """
            <div class="compact-section">
                <h3 style="color: #667eea; margin: 0.5rem 0; font-size: 1.2rem;">🎯 Search Results</h3>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Ensure session state for tracking deleted resumes
        if "deleted_resumes" not in st.session_state:
            st.session_state["deleted_resumes"] = set()

        # Filter resumes to exclude deleted ones
        visible_resumes = [res for res in sorted_resumes if res.get("_id") not in st.session_state["deleted_resumes"]]

        if visible_resumes:
            # Add sorting options
            col1, col2 = st.columns([2, 1])
            with col1:
                st.info(f"📊 Showing {len(visible_resumes)} resume(s) sorted by academic performance")
            with col2:
                if st.button("🔄 Refresh Results", use_container_width=True):
                    st.rerun()

            # Display resumes in a grid
            for i in range(0, len(visible_resumes), 2):
                cols = st.columns(2)

                # Left resume
                with cols[0]:
                    CDisplayResume.display_resume_with_delete(visible_resumes[i])

                # Right resume, if available
                if i + 1 < len(visible_resumes):
                    with cols[1]:
                        CDisplayResume.display_resume_with_delete(visible_resumes[i+1])
                else:
                    cols[1].empty()
        else:
            st.warning("🔍 No resumes match your search criteria. Try adjusting your search terms.")
    elif not show_all and not st.session_state["resumes"]:
        # Show helpful message when no search has been performed
        st.markdown(
            """
            <div class="compact-ready-section">
                <h4 style="color: #667eea; margin: 0.5rem 0;">🚀 Ready to Search!</h4>
                <p style="color: #6c757d; font-size: 0.9rem; margin: 0.3rem 0; line-height: 1.4;">
                    Use the search box above to find resumes using natural language, or click "Show All" to view all resumes.
                </p>
                <p style="color: #6c757d; font-size: 0.8rem; margin: 0.3rem 0;">
                    💡 Try: "Find Python developers", "Show teachers with 5+ years experience"
                </p>
            </div>
            """,
            unsafe_allow_html=True
        )

    # Compact footer
    st.markdown(
        """
        <div style="text-align: center; padding: 0.8rem; background: rgba(102, 126, 234, 0.05); border-radius: 8px; margin-top: 1rem; border-top: 1px solid rgba(102, 126, 234, 0.1);">
            <p style="color: #6c757d; margin: 0; font-size: 0.8rem;">
                🤖 Resume AI Agent v2.0 • Powered by OpenAI GPT-4 • Built with Streamlit
            </p>
        </div>
        """,
        unsafe_allow_html=True
    )


if __name__ == "__main__":
    main()