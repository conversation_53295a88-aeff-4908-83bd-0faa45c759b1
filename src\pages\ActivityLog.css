/* ActivityLog.css */
.activity-log-page {
  animation: fadeIn 0.5s ease-in;
}

.activity-container {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.activity-stats {
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.activity-list {
  padding: 0;
  min-height: 400px;
  max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 32px;
  border-bottom: 1px solid var(--border-secondary);
  border-radius: 12px;
  margin: 8px 16px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  transform-origin: center;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover {
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--border-primary);
  margin: 6px 14px;
  padding: 22px 34px;
}

.activity-item:hover::before {
  opacity: 1;
}

.activity-item.clickable {
  cursor: pointer;
}

.activity-item.clickable:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: var(--accent-primary);
  margin: 4px 12px;
  padding: 24px 36px;
}

.activity-item.clickable:hover::before {
  opacity: 1;
  background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);
}

.activity-item.clickable:active {
  transform: translateY(-1px);
  transition: all 0.1s ease;
}

.activity-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.activity-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover .activity-icon-wrapper {
  transform: scale(1.05) rotate(2deg);
  background: var(--bg-primary);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.activity-item:hover .activity-icon-wrapper::before {
  opacity: 1;
}

.activity-item.clickable:hover .activity-icon-wrapper {
  transform: scale(1.08) rotate(-2deg);
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
  }
}

.activity-icon {
  width: 20px;
  height: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
}

.activity-icon.upload {
  color: #10b981;
}

.activity-icon.search {
  color: #f59e0b;
}

.activity-icon.process {
  color: #8b5cf6;
}

.activity-icon.bulk {
  color: #3b82f6;
}

.activity-icon.duplicate {
  color: #f59e0b;
}

.activity-icon.download {
  color: #06b6d4;
}

.activity-icon.delete {
  color: #ef4444;
}

.activity-icon.export {
  color: #84cc16;
}

.activity-icon.system {
  color: #6366f1;
}

.activity-icon.error {
  color: #f97316;
}

.activity-icon.default {
  color: #6b7280;
}

.activity-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-tertiary);
  text-align: center;
}

.activity-empty .empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.activity-empty p {
  font-size: 16px;
  margin: 0;
}

.activity-content {
  flex: 1;
  transition: all 0.3s ease;
}

.activity-item:hover .activity-content {
  transform: translateX(2px);
}

.activity-main {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.activity-item:hover .activity-main {
  color: var(--accent-primary);
}

.activity-action {
  font-weight: 500;
  transition: all 0.3s ease;
}

.activity-item.clickable:hover .activity-action {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.activity-detail {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.activity-item:hover .activity-detail {
  color: var(--text-primary);
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
}

.activity-item:hover .activity-meta {
  color: var(--text-secondary);
  transform: translateY(-1px);
}

.activity-user {
  font-weight: 500;
  transition: all 0.3s ease;
}

.activity-item:hover .activity-user {
  color: var(--accent-primary);
  font-weight: 600;
}

/* Dark theme enhancements */
[data-theme="dark"] .activity-item:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
}

[data-theme="dark"] .activity-item.clickable:hover {
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.15);
}

[data-theme="dark"] .activity-item:hover .activity-icon-wrapper {
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

/* Loading state animation */
.activity-item.loading {
  opacity: 0.7;
  animation: activityLoading 1.5s ease-in-out infinite;
}

@keyframes activityLoading {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.9; }
}

/* Activity List Scrollbar Styling - Blue theme to match Activity Log */
.activity-list::-webkit-scrollbar {
  width: 8px;
}

.activity-list::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.activity-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 4px;
  border: 1px solid var(--bg-primary);
}

.activity-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

/* Dark theme scrollbar styling - Enhanced blue theme */
[data-theme="dark"] .activity-list::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="dark"] .activity-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border: 1px solid var(--bg-primary);
}

[data-theme="dark"] .activity-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
}

/* Focus states for accessibility */
.activity-item.clickable:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

.activity-item.clickable:focus:not(:hover) {
  transform: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .activity-stats {
    padding: 20px;
  }

  .activity-item {
    padding: 16px 20px;
    margin: 4px 8px;
  }

  .activity-item:hover {
    transform: translateY(-1px);
    margin: 2px 6px;
    padding: 18px 22px;
  }

  .activity-item.clickable:hover {
    transform: translateY(-1px);
    margin: 2px 6px;
    padding: 18px 22px;
  }

  .activity-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .activity-icon-wrapper {
    width: 36px;
    height: 36px;
  }

  .activity-icon {
    width: 18px;
    height: 18px;
  }
}
