import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiActivity, FiUpload, FiSearch, FiFileText, FiUsers, FiClock, FiDownload, FiTrash2, FiSettings, FiArrowRight, FiCopy } from 'react-icons/fi';
import { toast } from 'react-toastify';
import './ActivityLog.css';

const ActivityLog = () => {
  const navigate = useNavigate();
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  // Utility function to format timestamp to relative time
  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return 'Just now';

    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now - activityTime) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return `${Math.floor(diffInSeconds / 604800)} weeks ago`;
  };

  // Enhanced function to format activity details
  const formatActivityDetail = (activity) => {
    const { action, detail } = activity;

    // Extract meaningful information from the detail
    if (action.includes('Resume uploaded') && detail) {
      return detail; // Show person name
    }
    if (action.includes('Resume processed successfully') && detail) {
      return detail; // Show person name
    }
    if (action.includes('Duplicate resume detected') && detail) {
      return detail; // Show person name
    }
    if (action.includes('Resume processing failed') && detail) {
      return detail; // Show filename
    }
    if (action.includes('Search completed') && detail) {
      // Extract query and results count from detail like '"Python developers" (15 results)'
      const match = detail.match(/^"([^"]+)"\s*\((\d+)\s*results?\)$/);
      if (match) {
        return `${match[1]} (${match[2]} results)`;
      }
      return detail;
    }
    if (action.includes('Documents processed') && detail) {
      return detail; // Show processing details
    }
    if (action.includes('PDF downloaded') && detail) {
      // Extract filename from detail like 'Resume ID: 123, File: filename.pdf'
      const match = detail.match(/File:\s*(.+)$/);
      if (match) {
        return match[1];
      }
      return detail;
    }

    return detail || '';
  };

  // Function to handle activity click
  const handleActivityClick = async (activity) => {
    const { action, detail } = activity;

    // Handle search activity clicks
    if (action.includes('Search completed')) {
      // Extract search query from detail like '"Python developers" (15 results)'
      const match = detail.match(/^"([^"]+)"/);
      if (match) {
        const searchQuery = match[1];
        // Navigate to search page with the query and switch to history tab
        navigate('/search', {
          state: {
            searchQuery: searchQuery,
            openHistoryTab: true
          }
        });
        return;
      }
    }

    // Handle successful resume processing clicks
    if (action.includes('Resume processed successfully')) {
      const personName = detail;
      try {
        // Search for the resume by person name
        const response = await fetch(`http://localhost:8002/query/${encodeURIComponent(personName)}`);
        if (response.ok) {
          const searchData = await response.json();
          if (searchData.results && searchData.results.length > 0) {
            // Navigate to search page with the person's name and show results
            navigate('/search', {
              state: {
                searchQuery: personName,
                searchResults: searchData.results,
                showResults: true
              }
            });
            return;
          }
        }
      } catch (error) {
        console.error('Error searching for resume:', error);
      }

      // Fallback: navigate to all resumes page
      navigate('/resumes');
    }
  };

  // Function to check if activity is clickable
  const isActivityClickable = (activity) => {
    const { action } = activity;
    return action.includes('Search completed') || action.includes('Resume processed successfully');
  };

  // Function to fetch real activity data from backend
  const fetchActivityData = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/activity');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.activities || [];
    } catch (error) {
      console.error('Error fetching activity data:', error);
      toast.error('Failed to load activity data');
      return [];
    }
  };

  useEffect(() => {
    const loadActivityData = async () => {
      setLoading(true);
      try {
        const activityData = await fetchActivityData();

        // Filter out system activities (Sample data loaded, System started, Advanced processing completed)
        const filteredActivities = activityData.filter(activity => {
          const action = activity.action || '';
          return !action.includes('Sample data loaded') &&
                 !action.includes('System started') &&
                 !action.includes('Advanced processing completed');
        });

        // Sort by timestamp (newest first)
        const sortedActivities = filteredActivities.sort((a, b) =>
          new Date(b.timestamp) - new Date(a.timestamp)
        );
        setActivities(sortedActivities);
      } catch (error) {
        console.error('Error loading activity data:', error);
        toast.error('Failed to load activity log');
      } finally {
        setLoading(false);
      }
    };

    loadActivityData();
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'upload':
        return <FiUpload className="activity-icon upload" />;
      case 'search':
        return <FiSearch className="activity-icon search" />;
      case 'process':
        return <FiFileText className="activity-icon process" />;
      case 'bulk':
        return <FiUsers className="activity-icon bulk" />;
      case 'duplicate':
        return <FiCopy className="activity-icon duplicate" />;
      case 'download':
        return <FiDownload className="activity-icon download" />;
      case 'delete':
        return <FiTrash2 className="activity-icon delete" />;
      case 'export':
        return <FiArrowRight className="activity-icon export" />;
      case 'system':
        return <FiSettings className="activity-icon system" />;
      case 'error':
        return <FiActivity className="activity-icon error" />;
      default:
        return <FiActivity className="activity-icon default" />;
    }
  };

  if (loading) {
    return (
      <div className="activity-log-page">
        <div className="loading-container">
          <div className="loading-spinner large" />
          <p>Loading activity log...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="activity-log-page">
      <div className="page-header">
        <h1>Activity Log</h1>
        <p>Track all system activities and user actions</p>
      </div>

      <div className="activity-container">
        <div className="activity-stats">
          <div className="stat-item">
            <FiClock size={16} />
            <span>{activities.length} total activities</span>
          </div>
          <div className="stat-item">
            <FiActivity size={16} />
            <span>
              {activities.filter(a => {
                const activityTime = new Date(a.timestamp);
                const today = new Date();
                return activityTime.toDateString() === today.toDateString();
              }).length} activities today
            </span>
          </div>
        </div>

        <div className="activity-list">
          {activities.length === 0 ? (
            <div className="activity-empty">
              <FiActivity className="empty-icon" />
              <p>No activities found</p>
            </div>
          ) : (
            activities.map(activity => (
              <div
                key={activity.id}
                className={`activity-item ${isActivityClickable(activity) ? 'clickable' : ''}`}
                onClick={() => isActivityClickable(activity) && handleActivityClick(activity)}
                style={{ cursor: isActivityClickable(activity) ? 'pointer' : 'default' }}
              >
                <div className="activity-icon-wrapper">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="activity-content">
                  <div className="activity-main">
                    <span className="activity-action">{activity.action}</span>
                    {formatActivityDetail(activity) && (
                      <span className="activity-detail">: {formatActivityDetail(activity)}</span>
                    )}
                  </div>
                  <div className="activity-meta">
                    <span className="activity-user">by {activity.user || 'Admin'}</span>
                    <span className="activity-time">{formatTimeAgo(activity.timestamp)}</span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivityLog;
